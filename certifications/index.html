<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/Portfolio/_next/static/media/569ce4b8f30dc480-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/Portfolio/_next/static/media/93f479601ee12b01-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/Portfolio/_next/static/css/b38ed3232d1ac3ed.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/Portfolio/_next/static/chunks/webpack-bb1ce731c3485fda.js"/><script src="/Portfolio/_next/static/chunks/4bd1b696-b515ed9a247c157e.js" async=""></script><script src="/Portfolio/_next/static/chunks/684-12c9a12be803a644.js" async=""></script><script src="/Portfolio/_next/static/chunks/main-app-7592dedec9215eed.js" async=""></script><script src="/Portfolio/_next/static/chunks/244-d2c75447083c6d14.js" async=""></script><script src="/Portfolio/_next/static/chunks/766-65a6f42e13a3b29f.js" async=""></script><script src="/Portfolio/_next/static/chunks/app/not-found-9c1ea0fd1bd84912.js" async=""></script><script src="/Portfolio/_next/static/chunks/725-1a48ce81ca026ab7.js" async=""></script><script src="/Portfolio/_next/static/chunks/app/certifications/page-db2e6a01a611f72c.js" async=""></script><meta name="next-size-adjust" content=""/><link rel="icon" href="/Portfolio/favicon.ico"/><title>Certifications | Ankush Gupta | Ankush Gupta</title><meta name="description" content="Explore the certifications and credentials earned by Ankush Gupta in machine learning, web development, and cloud technologies."/><meta name="application-name" content="Ankush Gupta Portfolio"/><meta name="author" content="Ankush Gupta"/><meta name="generator" content="Next.js"/><meta name="keywords" content="Certifications, Credentials, ML Certifications, Web Development Certifications, Cloud Certifications"/><meta name="referrer" content="origin-when-cross-origin"/><meta name="creator" content="Ankush Gupta"/><meta name="publisher" content="Ankush Gupta"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><link rel="canonical" href="https://ankushgitrepo.github.io/Portfolio/"/><meta name="format-detection" content="telephone=no, address=no, email=no"/><meta property="og:title" content="Certifications | Ankush Gupta"/><meta property="og:description" content="Explore the certifications and credentials earned by Ankush Gupta in machine learning, web development, and cloud technologies."/><meta property="og:image" content="https://ankushgitrepo.github.io/Portfolio/Portfolio/images/og-image.jpg"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="Certifications | Ankush Gupta"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="Certifications | Ankush Gupta"/><meta name="twitter:description" content="Explore the certifications and credentials earned by Ankush Gupta in machine learning, web development, and cloud technologies."/><meta name="twitter:image" content="https://ankushgitrepo.github.io/Portfolio/Portfolio/images/og-image.jpg"/><link rel="icon" href="/Portfolio/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/Portfolio/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_170e2b __variable_15ed36 antialiased"><div class="flex flex-col min-h-screen"><header class="fixed top-0 left-0 w-full z-50 transition-all duration-300 bg-gradient-to-r from-blue-50 to-blue-100 bg-opacity-80"><div class="container mx-auto px-4 py-4 flex justify-between items-center"><a class="text-2xl font-bold text-blue-800 transition-colors duration-300" href="/Portfolio/">Ankush Gupta</a><nav class="hidden md:flex space-x-8"><a class="text-lg transition-colors duration-300 text-blue-800 hover:text-blue-600" href="/Portfolio/">Home</a><a class="text-lg transition-colors duration-300 text-blue-800 hover:text-blue-600" href="/Portfolio/about/">About</a><a class="text-lg transition-colors duration-300 text-blue-800 hover:text-blue-600" href="/Portfolio/projects/">Projects</a><a class="text-lg transition-colors duration-300 text-blue-800 hover:text-blue-600" href="/Portfolio/skills/">Skills</a><a class="text-lg transition-colors duration-300 text-blue-800 hover:text-blue-600" href="/Portfolio/contact/">Contact</a></nav><button class="md:hidden focus:outline-none text-blue-800 transition-colors duration-300" aria-label="Toggle menu"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600 transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg></button></div></header><main class="flex-grow pt-16"><script type="application/ld+json">{"@context":"https://schema.org","@type":"WebSite","name":"Certifications | Ankush Gupta","url":"https://ankushgitrepo.github.io/Portfolio/certifications","description":"Explore the certifications and credentials earned by Ankush Gupta in machine learning, web development, and cloud technologies."}</script><section class="py-20 mt-0 bg-gradient-to-b from-blue-50 to-blue-100 transition-colors duration-500 px-4 sm:px-6 lg:px-8"><div class="container mx-auto"><div class="mb-8 text-center"><h1 class="text-4xl font-bold text-blue-600 mb-4">Certifications</h1><p class="text-gray-600 max-w-2xl mx-auto">A collection of my professional certifications and credentials in machine learning, web development, and cloud technologies.</p></div><div class="mb-8"><div class="flex items-center justify-center mb-2"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-funnel mr-2 text-gray-600" aria-hidden="true"><path d="M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z"></path></svg><h2 class="text-lg font-medium text-gray-700">Filter by Category</h2></div><div class="flex flex-wrap justify-center gap-2 mb-2"><button class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 shadow-sm rounded-full text-sm py-1 px-3 bg-blue-600 hover:bg-blue-700">All</button><button class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2 border border-gray-200 hover:bg-gray-100 text-gray-900 shadow-sm rounded-full text-sm py-1 px-3 hover:bg-gray-100">Languages</button><button class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2 border border-gray-200 hover:bg-gray-100 text-gray-900 shadow-sm rounded-full text-sm py-1 px-3 hover:bg-gray-100">Skills</button><button class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2 border border-gray-200 hover:bg-gray-100 text-gray-900 shadow-sm rounded-full text-sm py-1 px-3 hover:bg-gray-100">Tools</button><button class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2 border border-gray-200 hover:bg-gray-100 text-gray-900 shadow-sm rounded-full text-sm py-1 px-3 hover:bg-gray-100">Workshop</button><button class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2 border border-gray-200 hover:bg-gray-100 text-gray-900 shadow-sm rounded-full text-sm py-1 px-3 hover:bg-gray-100">AI/ML</button><button class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2 border border-gray-200 hover:bg-gray-100 text-gray-900 shadow-sm rounded-full text-sm py-1 px-3 hover:bg-gray-100">Hackathon</button></div></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"><div class="rounded-lg overflow-hidden border border-gray-200 shadow-sm animate-pulse"><div class="h-48 bg-gray-200"></div><div class="p-4"><div class="h-6 bg-gray-200 rounded w-3/4 mb-3"></div><div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-4 bg-gray-200 rounded w-1/3 mb-4"></div><div class="flex gap-2 mb-4"><div class="h-6 bg-gray-200 rounded w-16"></div><div class="h-6 bg-gray-200 rounded w-16"></div></div><div class="h-4 bg-gray-200 rounded w-24 mt-2"></div></div></div><div class="rounded-lg overflow-hidden border border-gray-200 shadow-sm animate-pulse"><div class="h-48 bg-gray-200"></div><div class="p-4"><div class="h-6 bg-gray-200 rounded w-3/4 mb-3"></div><div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-4 bg-gray-200 rounded w-1/3 mb-4"></div><div class="flex gap-2 mb-4"><div class="h-6 bg-gray-200 rounded w-16"></div><div class="h-6 bg-gray-200 rounded w-16"></div></div><div class="h-4 bg-gray-200 rounded w-24 mt-2"></div></div></div><div class="rounded-lg overflow-hidden border border-gray-200 shadow-sm animate-pulse"><div class="h-48 bg-gray-200"></div><div class="p-4"><div class="h-6 bg-gray-200 rounded w-3/4 mb-3"></div><div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-4 bg-gray-200 rounded w-1/3 mb-4"></div><div class="flex gap-2 mb-4"><div class="h-6 bg-gray-200 rounded w-16"></div><div class="h-6 bg-gray-200 rounded w-16"></div></div><div class="h-4 bg-gray-200 rounded w-24 mt-2"></div></div></div><div class="rounded-lg overflow-hidden border border-gray-200 shadow-sm animate-pulse"><div class="h-48 bg-gray-200"></div><div class="p-4"><div class="h-6 bg-gray-200 rounded w-3/4 mb-3"></div><div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-4 bg-gray-200 rounded w-1/3 mb-4"></div><div class="flex gap-2 mb-4"><div class="h-6 bg-gray-200 rounded w-16"></div><div class="h-6 bg-gray-200 rounded w-16"></div></div><div class="h-4 bg-gray-200 rounded w-24 mt-2"></div></div></div><div class="rounded-lg overflow-hidden border border-gray-200 shadow-sm animate-pulse"><div class="h-48 bg-gray-200"></div><div class="p-4"><div class="h-6 bg-gray-200 rounded w-3/4 mb-3"></div><div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-4 bg-gray-200 rounded w-1/3 mb-4"></div><div class="flex gap-2 mb-4"><div class="h-6 bg-gray-200 rounded w-16"></div><div class="h-6 bg-gray-200 rounded w-16"></div></div><div class="h-4 bg-gray-200 rounded w-24 mt-2"></div></div></div><div class="rounded-lg overflow-hidden border border-gray-200 shadow-sm animate-pulse"><div class="h-48 bg-gray-200"></div><div class="p-4"><div class="h-6 bg-gray-200 rounded w-3/4 mb-3"></div><div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div><div class="h-4 bg-gray-200 rounded w-1/3 mb-4"></div><div class="flex gap-2 mb-4"><div class="h-6 bg-gray-200 rounded w-16"></div><div class="h-6 bg-gray-200 rounded w-16"></div></div><div class="h-4 bg-gray-200 rounded w-24 mt-2"></div></div></div></div></div></section></main><footer class="relative py-12 border-t border-gray-100 overflow-hidden shadow-sm bg-white"><div class="absolute inset-0 z-0"><div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100 opacity-20"></div><div class="absolute inset-0 bg-grid-pattern opacity-3"></div><div class="absolute -right-20 -bottom-20 w-64 h-64 rounded-full bg-blue-600 hover:bg-blue-700 opacity-10"></div><div class="absolute -left-20 -top-20 w-64 h-64 rounded-full bg-blue-600 hover:bg-blue-700 opacity-10"></div><div class="absolute inset-0 bg-noise opacity-[0.02]"></div></div><div class="absolute top-0 left-0 right-0 h-1 bg-blue-600 hover:bg-blue-700 z-10 transition-colors duration-500"></div><div class="absolute top-12 right-12 w-24 h-24 rounded-full bg-blue-600 hover:bg-blue-700 opacity-5 blur-xl"></div><div class="absolute bottom-12 left-12 w-32 h-32 rounded-full bg-blue-600 hover:bg-blue-700 opacity-5 blur-xl"></div><div class="container mx-auto px-4 relative z-20"><div class="relative h-0.5 w-full mb-10 overflow-hidden"><div class="absolute inset-0 bg-blue-600 hover:bg-blue-700 opacity-70"></div><div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent w-1/3 animate-shimmer"></div></div><div class="grid grid-cols-1 md:grid-cols-4 gap-8"><div class="md:col-span-1"><h3 class="text-xl font-bold text-blue-600 mb-4 drop-shadow-sm">Ankush Gupta</h3><p class="text-gray-700 mb-4">ML Engineer &amp; Full Stack Developer specializing in building exceptional digital experiences.</p><div class="flex space-x-4"><a href="https://github.com/AnkushGitRepo" target="_blank" rel="noopener noreferrer" class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" aria-label="GitHub"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"></path></svg></a><a href="https://linkedin.com/in/ankushgupta18" target="_blank" rel="noopener noreferrer" class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" aria-label="LinkedIn"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z"></path></svg></a><a href="https://instagram.com/_ankushg" target="_blank" rel="noopener noreferrer" class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" aria-label="Instagram"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"></path></svg></a><a href="mailto:<EMAIL>" class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" aria-label="Email"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg></a></div></div><div class="md:col-span-1"><h3 class="text-xl font-bold text-blue-600 mb-4 drop-shadow-sm">Main Pages</h3><ul class="space-y-2"><li><a class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" href="/Portfolio/">Home</a></li><li><a class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" href="/Portfolio/about/">About Me</a></li><li><a class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" href="/Portfolio/projects/">Projects</a></li><li><a class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" href="/Portfolio/skills/">Skills</a></li><li><a class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" href="/Portfolio/contact/">Contact Me</a></li></ul></div><div class="md:col-span-1"><h3 class="text-xl font-bold text-blue-600 mb-4 drop-shadow-sm">Resources</h3><ul class="space-y-2"><li><a class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" href="/Portfolio/books/">Books</a></li><li><a class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" href="/Portfolio/certifications/">Certifications</a></li><li><a href="/resume.pdf" target="_blank" rel="noopener noreferrer" class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium">Resume</a></li></ul></div><div class="md:col-span-1"><h3 class="text-xl font-bold text-blue-600 mb-4 drop-shadow-sm">Contact</h3><p class="text-gray-700 mb-2"><span class="font-medium">Email:</span> <EMAIL></p><p class="text-gray-700 mb-2"><span class="font-medium">Location:</span> Ahmedabad, Gujarat, India</p><p class="text-gray-700 mb-2"><span class="font-medium">Phone:</span> +91 7202906881</p></div></div><div class="border-t border-gray-200 mt-8 pt-8 text-center relative"><div class="absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div><div class="w-20 h-1 bg-blue-600 hover:bg-blue-700 mx-auto mb-6"></div><p class="text-gray-700 font-medium">© <!-- -->2025<!-- --> <span class="text-blue-600">Ankush Gupta</span>. All rights reserved.</p></div></div></footer></div><!--$--><!--/$--><!--$--><!--/$--><script src="/Portfolio/_next/static/chunks/webpack-bb1ce731c3485fda.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[1944,[\"244\",\"static/chunks/244-d2c75447083c6d14.js\",\"766\",\"static/chunks/766-65a6f42e13a3b29f.js\",\"345\",\"static/chunks/app/not-found-9c1ea0fd1bd84912.js\"],\"default\"]\n6:I[9665,[],\"MetadataBoundary\"]\n8:I[9665,[],\"OutletBoundary\"]\nb:I[4911,[],\"AsyncMetadataOutlet\"]\nd:I[9665,[],\"ViewportBoundary\"]\nf:I[6614,[],\"\"]\n:HL[\"/Portfolio/_next/static/media/569ce4b8f30dc480-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/Portfolio/_next/static/media/93f479601ee12b01-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/Portfolio/_next/static/css/b38ed3232d1ac3ed.css\",\"style\"]\n0:{\"P\":null,\"b\":\"NdbhGYNvIFHtDN1vFTF84\",\"p\":\"/Portfolio\",\"c\":[\"\",\"certifications\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"certifications\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/Portfolio/_next/static/css/b38ed3232d1ac3ed.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"meta\",null,{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"link\",null,{\"rel\":\"icon\",\"href\":\"/Portfolio/favicon.ico\"}]]}],[\"$\",\"body\",null,{\"className\":\"__variable_170e2b __variable_15ed36 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L4\",null,{}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]]}]]}],{\"children\":[\"certifications\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"childr"])</script><script>self.__next_f.push([1,"en\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L5\",[\"$\",\"$L6\",null,{\"children\":\"$L7\"}],null,[\"$\",\"$L8\",null,{\"children\":[\"$L9\",\"$La\",[\"$\",\"$Lb\",null,{\"promise\":\"$@c\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"jfpj2SWzqNMf_1uHg3GDk\",{\"children\":[[\"$\",\"$Ld\",null,{\"children\":\"$Le\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$f\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"10:\"$Sreact.suspense\"\n11:I[4911,[],\"AsyncMetadata\"]\n7:[\"$\",\"$10\",null,{\"fallback\":null,\"children\":[\"$\",\"$L11\",null,{\"promise\":\"$@12\"}]}]\n"])</script><script>self.__next_f.push([1,"a:null\n"])</script><script>self.__next_f.push([1,"e:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,"12:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Certifications | Ankush Gupta | Ankush Gupta\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Explore the certifications and credentials earned by Ankush Gupta in machine learning, web development, and cloud technologies.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Ankush Gupta Portfolio\"}],[\"$\",\"meta\",\"3\",{\"name\":\"author\",\"content\":\"Ankush Gupta\"}],[\"$\",\"meta\",\"4\",{\"name\":\"generator\",\"content\":\"Next.js\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"Certifications, Credentials, ML Certifications, Web Development Certifications, Cloud Certifications\"}],[\"$\",\"meta\",\"6\",{\"name\":\"referrer\",\"content\":\"origin-when-cross-origin\"}],[\"$\",\"meta\",\"7\",{\"name\":\"creator\",\"content\":\"Ankush Gupta\"}],[\"$\",\"meta\",\"8\",{\"name\":\"publisher\",\"content\":\"Ankush Gupta\"}],[\"$\",\"meta\",\"9\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"10\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"link\",\"11\",{\"rel\":\"canonical\",\"href\":\"https://ankushgitrepo.github.io/Portfolio/\"}],[\"$\",\"meta\",\"12\",{\"name\":\"format-detection\",\"content\":\"telephone=no, address=no, email=no\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:title\",\"content\":\"Certifications | Ankush Gupta\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:description\",\"content\":\"Explore the certifications and credentials earned by Ankush Gupta in machine learning, web development, and cloud technologies.\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:image\",\"content\":\"https://ankushgitrepo.github.io/Portfolio/Portfolio/images/og-image.jpg\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"18\",{\"property\":\"og:image:alt\",\"content\":\"Certifications | Ankush Gupta\"}],[\"$\",\"meta\",\"19\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:title\",\"content\":\"Certifications | Ankush Gupta\"}],[\"$\",\"meta\",\"22\",{\"name\":\"twitter:description\",\"content\":\"Explore the certifications and credentials earned by Ankush Gupta in machine learning, web development, and cloud technologies.\"}],[\"$\",\"meta\",\"23\",{\"name\":\"twitter:image\",\"content\":\"https://ankushgitrepo.github.io/Portfolio/Portfolio/images/og-image.jpg\"}],[\"$\",\"link\",\"24\",{\"rel\":\"icon\",\"href\":\"/Portfolio/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"c:{\"metadata\":\"$12:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"13:I[1359,[\"244\",\"static/chunks/244-d2c75447083c6d14.js\",\"766\",\"static/chunks/766-65a6f42e13a3b29f.js\",\"725\",\"static/chunks/725-1a48ce81ca026ab7.js\",\"736\",\"static/chunks/app/certifications/page-db2e6a01a611f72c.js\"],\"ThemeColorProvider\"]\n14:I[3725,[\"244\",\"static/chunks/244-d2c75447083c6d14.js\",\"766\",\"static/chunks/766-65a6f42e13a3b29f.js\",\"725\",\"static/chunks/725-1a48ce81ca026ab7.js\",\"736\",\"static/chunks/app/certifications/page-db2e6a01a611f72c.js\"],\"default\"]\n15:I[3840,[\"244\",\"static/chunks/244-d2c75447083c6d14.js\",\"766\",\"static/chunks/766-65a6f42e13a3b29f.js\",\"725\",\"static/chunks/725-1a48ce81ca026ab7.js\",\"736\",\"static/chunks/app/certifications/page-db2e6a01a611f72c.js\"],\"default\"]\n"])</script><script>self.__next_f.push([1,"5:[\"$\",\"$L13\",null,{\"children\":[\"$\",\"$L14\",null,{\"children\":[[\"$\",\"script\",null,{\"type\":\"application/ld+json\",\"dangerouslySetInnerHTML\":{\"__html\":\"{\\\"@context\\\":\\\"https://schema.org\\\",\\\"@type\\\":\\\"WebSite\\\",\\\"name\\\":\\\"Certifications | Ankush Gupta\\\",\\\"url\\\":\\\"https://ankushgitrepo.github.io/Portfolio/certifications\\\",\\\"description\\\":\\\"Explore the certifications and credentials earned by Ankush Gupta in machine learning, web development, and cloud technologies.\\\"}\"}}],[\"$\",\"$L15\",null,{\"certifications\":[{\"_id\":\"1\",\"title\":\"Python 3 Programming\",\"issuer\":\"Coursera \u0026 University of Michigan\",\"description\":\"This specialization teaches the fundamentals of Python 3 programming. It covers Python basics, data structures, file handling, and advanced concepts like object-oriented programming. The specialization includes hands-on projects to build practical programming skills.\",\"image\":\"/images/certificates/Python_3_Programming.png\",\"issueDate\":\"2024-11-15\",\"credentialUrl\":\"https://www.coursera.org/account/accomplishments/specialization/DL6IDFS65YVP\",\"skills\":[\"Python\",\"Programming\",\"Data Structures\",\"Object-Oriented Programming\",\"File Handling\"],\"order\":1,\"createdAt\":\"2025-05-29T15:17:31.433Z\"},{\"_id\":\"2\",\"title\":\"Generative AI for Software Developers\",\"issuer\":\"Coursera \u0026 IBM\",\"description\":\"This specialization provides a comprehensive introduction to generative AI for software developers. It covers the fundamentals of generative models, prompt engineering, and how to integrate generative AI into software applications. The program includes practical projects to apply these concepts in real-world scenarios.\",\"image\":\"/images/certificates/Generative_AI_for_Software_Developers.png\",\"issueDate\":\"2024-11-25\",\"credentialUrl\":\"https://www.coursera.org/account/accomplishments/specialization/CONL6GU9WBG4\",\"skills\":[\"Generative AI\",\"Prompt Engineering\",\"Software Development\",\"AI Integration\",\"Machine Learning\"],\"order\":2,\"createdAt\":\"2025-05-29T15:17:31.433Z\"},{\"_id\":\"3\",\"title\":\"Foundations of Cybersecurity\",\"issuer\":\"Coursera \u0026 Google\",\"description\":\"This course introduces the core concepts of cybersecurity and provides a foundation for understanding security principles, threats, and defense mechanisms. It covers security frameworks, risk management, and basic security controls that organizations implement to protect their digital assets.\",\"image\":\"/images/certificates/Foundations_of_Cybersecurity.png\",\"issueDate\":\"2024-12-08\",\"credentialUrl\":\"https://www.coursera.org/account/accomplishments/verify/4RPCZ4P05PKB\",\"skills\":[\"Cybersecurity\",\"Security Frameworks\",\"Risk Management\",\"Network Security\",\"Security Controls\"],\"order\":3,\"createdAt\":\"2025-05-29T15:17:31.433Z\"},{\"_id\":\"4\",\"title\":\"Introduction to HTML, CSS, \u0026 JavaScript\",\"issuer\":\"Coursera \u0026 IBM\",\"description\":\"This course provides a comprehensive introduction to the three core technologies of web development: HTML for structure, CSS for styling, and JavaScript for interactivity. It covers the fundamentals of creating and styling web pages and adding dynamic functionality through JavaScript.\",\"image\":\"/images/certificates/Introduction_to_HTML_CSS_\u0026_JavaScript.png\",\"issueDate\":\"2024-12-05\",\"credentialUrl\":\"https://www.coursera.org/account/accomplishments/verify/NQN98HM46LB0\",\"skills\":[\"HTML\",\"CSS\",\"JavaScript\",\"Web Development\",\"Front-end Development\"],\"order\":4,\"createdAt\":\"2025-05-29T15:17:31.433Z\"},{\"_id\":\"5\",\"title\":\"Object-Oriented Design\",\"issuer\":\"Coursera \u0026 University of Alberta\",\"description\":\"This course explores the principles and concepts of object-oriented design and programming. It covers class design, inheritance, polymorphism, and design patterns. The course teaches how to apply these concepts to create flexible, maintainable, and reusable software systems.\",\"image\":\"/images/certificates/Object-Oriented_Design.png\",\"issueDate\":\"2024-12-24\",\"credentialUrl\":\"https://www.coursera.org/account/accomplishments/verify/K1N2D79JAUV8\",\"skills\":[\"Object-Oriented Programming\",\"Software Design\",\"Design Patterns\",\"UML\",\"Software Architecture\"],\"order\":5,\"createdAt\":\"2025-05-29T15:17:31.433Z\"},{\"_id\":\"6\",\"title\":\"Supervised Machine Learning: Regression and Classification\",\"issuer\":\"Coursera \u0026 Stanford University\",\"description\":\"This course provides a comprehensive introduction to supervised machine learning, focusing on regression and classification algorithms. It covers linear regression, logistic regression, gradient descent, and the fundamentals of model training and evaluation.\",\"image\":\"/images/certificates/Supervised_Machine_Learning.png\",\"issueDate\":\"2025-04-18\",\"credentialUrl\":\"https://www.coursera.org/account/accomplishments/verify/R48M58N8ANIF\",\"skills\":[\"Machine Learning\",\"Regression\",\"Classification\",\"Python\",\"Model Evaluation\"],\"order\":6,\"createdAt\":\"2025-05-29T15:17:31.433Z\"},{\"_id\":\"7\",\"title\":\"Programming with JavaScript\",\"issuer\":\"Coursera \u0026 Meta\",\"description\":\"This course teaches the fundamentals of programming with JavaScript. It covers core concepts like variables, data types, functions, loops, and DOM manipulation. The course includes practical exercises to build interactive web applications using JavaScript.\",\"image\":\"/images/certificates/Programming_with_JavaScript.png\",\"issueDate\":\"2025-04-18\",\"credentialUrl\":\"https://www.coursera.org/account/accomplishments/verify/IUJC6K6I5RWP\",\"skills\":[\"JavaScript\",\"Web Development\",\"DOM Manipulation\",\"Front-end Development\",\"Programming Logic\"],\"order\":7,\"createdAt\":\"2025-05-29T15:17:31.433Z\"},{\"_id\":\"8\",\"title\":\"Introduction to Java\",\"issuer\":\"Coursera \u0026 Learn Quest\",\"description\":\"This course introduces the fundamentals of Java programming. It covers basic syntax, object-oriented concepts, data structures, and file I/O in Java. The course includes hands-on programming assignments to build practical Java development skills.\",\"image\":\"/images/certificates/Introduction_to_Java.png\",\"issueDate\":\"2023-12-23\",\"credentialUrl\":\"https://www.coursera.org/account/accomplishments/verify/MUSB84MUEDSQ\",\"skills\":[\"Java\",\"Object-Oriented Programming\",\"Data Structures\",\"Software Development\",\"Programming\"],\"order\":8,\"createdAt\":\"2025-05-29T15:17:31.433Z\"},{\"_id\":\"9\",\"title\":\"Inheritance and Data Structures in Java\",\"issuer\":\"Coursera \u0026 University of Pennsylvania\",\"description\":\"This course builds on basic Java knowledge to explore advanced concepts like inheritance, interfaces, and data structures. It covers how to implement and use common data structures in Java and how to leverage inheritance for code reuse and extensibility.\",\"image\":\"/images/certificates/Inheritance_and_Data_Structures_in_Java.png\",\"issueDate\":\"2024-05-24\",\"credentialUrl\":\"https://www.coursera.org/account/accomplishments/verify/UKBCUY4VGUWH\",\"skills\":[\"Java\",\"Inheritance\",\"Data Structures\",\"Object-Oriented Programming\",\"Algorithms\"],\"order\":9,\"createdAt\":\"2025-05-29T15:17:31.433Z\"},{\"_id\":\"10\",\"title\":\"LinkedIn Workshop\",\"issuer\":\"GrowthSchool\",\"description\":\"This workshop provides comprehensive strategies for optimizing LinkedIn profiles and leveraging the platform for professional networking and career growth. It covers profile optimization, content creation, engagement strategies, and techniques for building a professional brand on LinkedIn.\",\"image\":\"/images/certificates/Linkedin_Workshop.png\",\"issueDate\":\"2025-03-19\",\"credentialUrl\":\"https://learners.growthschool.io/certificate/00bac5ff-9844-4120-9e57-fabd081a7027\",\"skills\":[\"LinkedIn\",\"Personal Branding\",\"Professional Networking\",\"Content Strategy\",\"Career Development\"],\"order\":10,\"createdAt\":\"2025-05-29T15:17:31.433Z\"},{\"_id\":\"11\",\"title\":\"2 Day AI Mastery Workshop\",\"issuer\":\"Outskill\",\"description\":\"This intensive two-day workshop covers the fundamentals and practical applications of artificial intelligence. Participants learn about machine learning algorithms, neural networks, and how to implement AI solutions for real-world problems. The workshop includes hands-on exercises and project-based learning.\",\"image\":\"/images/certificates/2_Day_AI_Mastery_Workshop.png\",\"issueDate\":\"2025-04-23\",\"credentialUrl\":\"\",\"skills\":[\"Artificial Intelligence\",\"Machine Learning\",\"Neural Networks\",\"AI Applications\",\"Problem Solving\"],\"order\":11,\"createdAt\":\"2025-05-29T15:17:31.433Z\"},{\"_id\":\"12\",\"title\":\"Ingenious Hackathon 6.0\",\"issuer\":\"Ahmedabad University\",\"description\":\"This certificate recognizes participation and achievement in the Ingenious Hackathon 6.0, a competitive event where participants develop innovative solutions to real-world problems within a limited timeframe. The hackathon focuses on creativity, technical skills, and teamwork in developing functional prototypes.\",\"image\":\"/images/certificates/AU_Hackathon.png\",\"issueDate\":\"2025-03-19\",\"credentialUrl\":\"\",\"skills\":[\"Hackathon\",\"Problem Solving\",\"Software Development\",\"Teamwork\",\"Innovation\"],\"order\":12,\"createdAt\":\"2025-05-29T15:17:31.433Z\"},{\"_id\":\"13\",\"title\":\"GitHub Foundations\",\"issuer\":\"GitHub\",\"description\":\"This certification validates proficiency in GitHub fundamentals, including repository management, version control, collaboration workflows, and best practices for using GitHub effectively. It demonstrates the ability to use GitHub for project management and code collaboration.\",\"image\":\"/images/certificates/GitHub_Foundations.png\",\"issueDate\":\"2025-02-02\",\"credentialUrl\":\"https://www.credly.com/badges/279d09f0-6b04-417e-82df-310670f7043a/print\",\"skills\":[\"GitHub\",\"Version Control\",\"Git\",\"Repository Management\",\"Collaboration\",\"Project Management\"],\"order\":13,\"createdAt\":\"2025-05-29T15:17:31.433Z\"}]}]]}]}]\n"])</script></body></html>