1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
4:I[1944,["244","static/chunks/244-d2c75447083c6d14.js","766","static/chunks/766-65a6f42e13a3b29f.js","345","static/chunks/app/not-found-9c1ea0fd1bd84912.js"],"default"]
6:I[9665,[],"MetadataBoundary"]
8:I[9665,[],"OutletBoundary"]
b:I[4911,[],"AsyncMetadataOutlet"]
d:I[9665,[],"ViewportBoundary"]
f:I[6614,[],""]
:HL["/Portfolio/_next/static/media/569ce4b8f30dc480-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/Portfolio/_next/static/media/93f479601ee12b01-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/Portfolio/_next/static/css/b38ed3232d1ac3ed.css","style"]
0:{"P":null,"b":"NdbhGYNvIFHtDN1vFTF84","p":"/Portfolio","c":["","certifications",""],"i":false,"f":[[["",{"children":["certifications",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/Portfolio/_next/static/css/b38ed3232d1ac3ed.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[["$","meta",null,{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","link",null,{"rel":"icon","href":"/Portfolio/favicon.ico"}]]}],["$","body",null,{"className":"__variable_170e2b __variable_15ed36 antialiased","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","$L4",null,{}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]]}]]}],{"children":["certifications",["$","$1","c",{"children":[null,["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":["$L5",["$","$L6",null,{"children":"$L7"}],null,["$","$L8",null,{"children":["$L9","$La",["$","$Lb",null,{"promise":"$@c"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","jfpj2SWzqNMf_1uHg3GDk",{"children":[["$","$Ld",null,{"children":"$Le"}],["$","meta",null,{"name":"next-size-adjust","content":""}]]}],null]}],false]],"m":"$undefined","G":["$f","$undefined"],"s":false,"S":true}
10:"$Sreact.suspense"
11:I[4911,[],"AsyncMetadata"]
7:["$","$10",null,{"fallback":null,"children":["$","$L11",null,{"promise":"$@12"}]}]
a:null
e:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
9:null
12:{"metadata":[["$","title","0",{"children":"Certifications | Ankush Gupta | Ankush Gupta"}],["$","meta","1",{"name":"description","content":"Explore the certifications and credentials earned by Ankush Gupta in machine learning, web development, and cloud technologies."}],["$","meta","2",{"name":"application-name","content":"Ankush Gupta Portfolio"}],["$","meta","3",{"name":"author","content":"Ankush Gupta"}],["$","meta","4",{"name":"generator","content":"Next.js"}],["$","meta","5",{"name":"keywords","content":"Certifications, Credentials, ML Certifications, Web Development Certifications, Cloud Certifications"}],["$","meta","6",{"name":"referrer","content":"origin-when-cross-origin"}],["$","meta","7",{"name":"creator","content":"Ankush Gupta"}],["$","meta","8",{"name":"publisher","content":"Ankush Gupta"}],["$","meta","9",{"name":"robots","content":"index, follow"}],["$","meta","10",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","link","11",{"rel":"canonical","href":"https://ankushgitrepo.github.io/Portfolio/"}],["$","meta","12",{"name":"format-detection","content":"telephone=no, address=no, email=no"}],["$","meta","13",{"property":"og:title","content":"Certifications | Ankush Gupta"}],["$","meta","14",{"property":"og:description","content":"Explore the certifications and credentials earned by Ankush Gupta in machine learning, web development, and cloud technologies."}],["$","meta","15",{"property":"og:image","content":"https://ankushgitrepo.github.io/Portfolio/Portfolio/images/og-image.jpg"}],["$","meta","16",{"property":"og:image:width","content":"1200"}],["$","meta","17",{"property":"og:image:height","content":"630"}],["$","meta","18",{"property":"og:image:alt","content":"Certifications | Ankush Gupta"}],["$","meta","19",{"property":"og:type","content":"website"}],["$","meta","20",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","21",{"name":"twitter:title","content":"Certifications | Ankush Gupta"}],["$","meta","22",{"name":"twitter:description","content":"Explore the certifications and credentials earned by Ankush Gupta in machine learning, web development, and cloud technologies."}],["$","meta","23",{"name":"twitter:image","content":"https://ankushgitrepo.github.io/Portfolio/Portfolio/images/og-image.jpg"}],["$","link","24",{"rel":"icon","href":"/Portfolio/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
c:{"metadata":"$12:metadata","error":null,"digest":"$undefined"}
13:I[1359,["244","static/chunks/244-d2c75447083c6d14.js","766","static/chunks/766-65a6f42e13a3b29f.js","725","static/chunks/725-1a48ce81ca026ab7.js","736","static/chunks/app/certifications/page-db2e6a01a611f72c.js"],"ThemeColorProvider"]
14:I[3725,["244","static/chunks/244-d2c75447083c6d14.js","766","static/chunks/766-65a6f42e13a3b29f.js","725","static/chunks/725-1a48ce81ca026ab7.js","736","static/chunks/app/certifications/page-db2e6a01a611f72c.js"],"default"]
15:I[3840,["244","static/chunks/244-d2c75447083c6d14.js","766","static/chunks/766-65a6f42e13a3b29f.js","725","static/chunks/725-1a48ce81ca026ab7.js","736","static/chunks/app/certifications/page-db2e6a01a611f72c.js"],"default"]
5:["$","$L13",null,{"children":["$","$L14",null,{"children":[["$","script",null,{"type":"application/ld+json","dangerouslySetInnerHTML":{"__html":"{\"@context\":\"https://schema.org\",\"@type\":\"WebSite\",\"name\":\"Certifications | Ankush Gupta\",\"url\":\"https://ankushgitrepo.github.io/Portfolio/certifications\",\"description\":\"Explore the certifications and credentials earned by Ankush Gupta in machine learning, web development, and cloud technologies.\"}"}}],["$","$L15",null,{"certifications":[{"_id":"1","title":"Python 3 Programming","issuer":"Coursera & University of Michigan","description":"This specialization teaches the fundamentals of Python 3 programming. It covers Python basics, data structures, file handling, and advanced concepts like object-oriented programming. The specialization includes hands-on projects to build practical programming skills.","image":"/images/certificates/Python_3_Programming.png","issueDate":"2024-11-15","credentialUrl":"https://www.coursera.org/account/accomplishments/specialization/DL6IDFS65YVP","skills":["Python","Programming","Data Structures","Object-Oriented Programming","File Handling"],"order":1,"createdAt":"2025-05-29T15:17:31.433Z"},{"_id":"2","title":"Generative AI for Software Developers","issuer":"Coursera & IBM","description":"This specialization provides a comprehensive introduction to generative AI for software developers. It covers the fundamentals of generative models, prompt engineering, and how to integrate generative AI into software applications. The program includes practical projects to apply these concepts in real-world scenarios.","image":"/images/certificates/Generative_AI_for_Software_Developers.png","issueDate":"2024-11-25","credentialUrl":"https://www.coursera.org/account/accomplishments/specialization/CONL6GU9WBG4","skills":["Generative AI","Prompt Engineering","Software Development","AI Integration","Machine Learning"],"order":2,"createdAt":"2025-05-29T15:17:31.433Z"},{"_id":"3","title":"Foundations of Cybersecurity","issuer":"Coursera & Google","description":"This course introduces the core concepts of cybersecurity and provides a foundation for understanding security principles, threats, and defense mechanisms. It covers security frameworks, risk management, and basic security controls that organizations implement to protect their digital assets.","image":"/images/certificates/Foundations_of_Cybersecurity.png","issueDate":"2024-12-08","credentialUrl":"https://www.coursera.org/account/accomplishments/verify/4RPCZ4P05PKB","skills":["Cybersecurity","Security Frameworks","Risk Management","Network Security","Security Controls"],"order":3,"createdAt":"2025-05-29T15:17:31.433Z"},{"_id":"4","title":"Introduction to HTML, CSS, & JavaScript","issuer":"Coursera & IBM","description":"This course provides a comprehensive introduction to the three core technologies of web development: HTML for structure, CSS for styling, and JavaScript for interactivity. It covers the fundamentals of creating and styling web pages and adding dynamic functionality through JavaScript.","image":"/images/certificates/Introduction_to_HTML_CSS_&_JavaScript.png","issueDate":"2024-12-05","credentialUrl":"https://www.coursera.org/account/accomplishments/verify/NQN98HM46LB0","skills":["HTML","CSS","JavaScript","Web Development","Front-end Development"],"order":4,"createdAt":"2025-05-29T15:17:31.433Z"},{"_id":"5","title":"Object-Oriented Design","issuer":"Coursera & University of Alberta","description":"This course explores the principles and concepts of object-oriented design and programming. It covers class design, inheritance, polymorphism, and design patterns. The course teaches how to apply these concepts to create flexible, maintainable, and reusable software systems.","image":"/images/certificates/Object-Oriented_Design.png","issueDate":"2024-12-24","credentialUrl":"https://www.coursera.org/account/accomplishments/verify/K1N2D79JAUV8","skills":["Object-Oriented Programming","Software Design","Design Patterns","UML","Software Architecture"],"order":5,"createdAt":"2025-05-29T15:17:31.433Z"},{"_id":"6","title":"Supervised Machine Learning: Regression and Classification","issuer":"Coursera & Stanford University","description":"This course provides a comprehensive introduction to supervised machine learning, focusing on regression and classification algorithms. It covers linear regression, logistic regression, gradient descent, and the fundamentals of model training and evaluation.","image":"/images/certificates/Supervised_Machine_Learning.png","issueDate":"2025-04-18","credentialUrl":"https://www.coursera.org/account/accomplishments/verify/R48M58N8ANIF","skills":["Machine Learning","Regression","Classification","Python","Model Evaluation"],"order":6,"createdAt":"2025-05-29T15:17:31.433Z"},{"_id":"7","title":"Programming with JavaScript","issuer":"Coursera & Meta","description":"This course teaches the fundamentals of programming with JavaScript. It covers core concepts like variables, data types, functions, loops, and DOM manipulation. The course includes practical exercises to build interactive web applications using JavaScript.","image":"/images/certificates/Programming_with_JavaScript.png","issueDate":"2025-04-18","credentialUrl":"https://www.coursera.org/account/accomplishments/verify/IUJC6K6I5RWP","skills":["JavaScript","Web Development","DOM Manipulation","Front-end Development","Programming Logic"],"order":7,"createdAt":"2025-05-29T15:17:31.433Z"},{"_id":"8","title":"Introduction to Java","issuer":"Coursera & Learn Quest","description":"This course introduces the fundamentals of Java programming. It covers basic syntax, object-oriented concepts, data structures, and file I/O in Java. The course includes hands-on programming assignments to build practical Java development skills.","image":"/images/certificates/Introduction_to_Java.png","issueDate":"2023-12-23","credentialUrl":"https://www.coursera.org/account/accomplishments/verify/MUSB84MUEDSQ","skills":["Java","Object-Oriented Programming","Data Structures","Software Development","Programming"],"order":8,"createdAt":"2025-05-29T15:17:31.433Z"},{"_id":"9","title":"Inheritance and Data Structures in Java","issuer":"Coursera & University of Pennsylvania","description":"This course builds on basic Java knowledge to explore advanced concepts like inheritance, interfaces, and data structures. It covers how to implement and use common data structures in Java and how to leverage inheritance for code reuse and extensibility.","image":"/images/certificates/Inheritance_and_Data_Structures_in_Java.png","issueDate":"2024-05-24","credentialUrl":"https://www.coursera.org/account/accomplishments/verify/UKBCUY4VGUWH","skills":["Java","Inheritance","Data Structures","Object-Oriented Programming","Algorithms"],"order":9,"createdAt":"2025-05-29T15:17:31.433Z"},{"_id":"10","title":"LinkedIn Workshop","issuer":"GrowthSchool","description":"This workshop provides comprehensive strategies for optimizing LinkedIn profiles and leveraging the platform for professional networking and career growth. It covers profile optimization, content creation, engagement strategies, and techniques for building a professional brand on LinkedIn.","image":"/images/certificates/Linkedin_Workshop.png","issueDate":"2025-03-19","credentialUrl":"https://learners.growthschool.io/certificate/00bac5ff-9844-4120-9e57-fabd081a7027","skills":["LinkedIn","Personal Branding","Professional Networking","Content Strategy","Career Development"],"order":10,"createdAt":"2025-05-29T15:17:31.433Z"},{"_id":"11","title":"2 Day AI Mastery Workshop","issuer":"Outskill","description":"This intensive two-day workshop covers the fundamentals and practical applications of artificial intelligence. Participants learn about machine learning algorithms, neural networks, and how to implement AI solutions for real-world problems. The workshop includes hands-on exercises and project-based learning.","image":"/images/certificates/2_Day_AI_Mastery_Workshop.png","issueDate":"2025-04-23","credentialUrl":"","skills":["Artificial Intelligence","Machine Learning","Neural Networks","AI Applications","Problem Solving"],"order":11,"createdAt":"2025-05-29T15:17:31.433Z"},{"_id":"12","title":"Ingenious Hackathon 6.0","issuer":"Ahmedabad University","description":"This certificate recognizes participation and achievement in the Ingenious Hackathon 6.0, a competitive event where participants develop innovative solutions to real-world problems within a limited timeframe. The hackathon focuses on creativity, technical skills, and teamwork in developing functional prototypes.","image":"/images/certificates/AU_Hackathon.png","issueDate":"2025-03-19","credentialUrl":"","skills":["Hackathon","Problem Solving","Software Development","Teamwork","Innovation"],"order":12,"createdAt":"2025-05-29T15:17:31.433Z"},{"_id":"13","title":"GitHub Foundations","issuer":"GitHub","description":"This certification validates proficiency in GitHub fundamentals, including repository management, version control, collaboration workflows, and best practices for using GitHub effectively. It demonstrates the ability to use GitHub for project management and code collaboration.","image":"/images/certificates/GitHub_Foundations.png","issueDate":"2025-02-02","credentialUrl":"https://www.credly.com/badges/279d09f0-6b04-417e-82df-310670f7043a/print","skills":["GitHub","Version Control","Git","Repository Management","Collaboration","Project Management"],"order":13,"createdAt":"2025-05-29T15:17:31.433Z"}]}]]}]}]
