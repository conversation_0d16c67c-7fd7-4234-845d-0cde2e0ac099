<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/Portfolio/_next/static/media/569ce4b8f30dc480-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/Portfolio/_next/static/media/93f479601ee12b01-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" as="image" href="/Portfolio/images/img_1.jpg"/><link rel="stylesheet" href="/Portfolio/_next/static/css/b38ed3232d1ac3ed.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/Portfolio/_next/static/chunks/webpack-bb1ce731c3485fda.js"/><script src="/Portfolio/_next/static/chunks/4bd1b696-b515ed9a247c157e.js" async=""></script><script src="/Portfolio/_next/static/chunks/684-12c9a12be803a644.js" async=""></script><script src="/Portfolio/_next/static/chunks/main-app-7592dedec9215eed.js" async=""></script><script src="/Portfolio/_next/static/chunks/244-d2c75447083c6d14.js" async=""></script><script src="/Portfolio/_next/static/chunks/766-65a6f42e13a3b29f.js" async=""></script><script src="/Portfolio/_next/static/chunks/app/not-found-9c1ea0fd1bd84912.js" async=""></script><script src="/Portfolio/_next/static/chunks/725-1a48ce81ca026ab7.js" async=""></script><script src="/Portfolio/_next/static/chunks/app/about/page-884f5e3f6dc04188.js" async=""></script><meta name="next-size-adjust" content=""/><link rel="icon" href="/Portfolio/favicon.ico"/><title>About | Ankush Gupta | Ankush Gupta</title><meta name="description" content="Learn more about Ankush Gupta, an ML Engineer and Full Stack Developer with expertise in building intelligent and user-friendly applications."/><meta name="application-name" content="Ankush Gupta Portfolio"/><meta name="author" content="Ankush Gupta"/><meta name="generator" content="Next.js"/><meta name="keywords" content="About Ankush Gupta, ML Engineer, Full Stack Developer, Experience, Education"/><meta name="referrer" content="origin-when-cross-origin"/><meta name="creator" content="Ankush Gupta"/><meta name="publisher" content="Ankush Gupta"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><link rel="canonical" href="https://ankushgitrepo.github.io/Portfolio/"/><meta name="format-detection" content="telephone=no, address=no, email=no"/><meta property="og:title" content="About | Ankush Gupta"/><meta property="og:description" content="Learn more about Ankush Gupta, an ML Engineer and Full Stack Developer with expertise in building intelligent and user-friendly applications."/><meta property="og:image" content="https://ankushgitrepo.github.io/Portfolio/Portfolio/images/og-image.jpg"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="About | Ankush Gupta"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="About | Ankush Gupta"/><meta name="twitter:description" content="Learn more about Ankush Gupta, an ML Engineer and Full Stack Developer with expertise in building intelligent and user-friendly applications."/><meta name="twitter:image" content="https://ankushgitrepo.github.io/Portfolio/Portfolio/images/og-image.jpg"/><link rel="icon" href="/Portfolio/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/Portfolio/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_170e2b __variable_15ed36 antialiased"><div class="flex flex-col min-h-screen"><header class="fixed top-0 left-0 w-full z-50 transition-all duration-300 bg-gradient-to-r from-blue-50 to-blue-100 bg-opacity-80"><div class="container mx-auto px-4 py-4 flex justify-between items-center"><a class="text-2xl font-bold text-blue-800 transition-colors duration-300" href="/Portfolio/">Ankush Gupta</a><nav class="hidden md:flex space-x-8"><a class="text-lg transition-colors duration-300 text-blue-800 hover:text-blue-600" href="/Portfolio/">Home</a><a class="text-lg transition-colors duration-300 text-blue-800 hover:text-blue-600" href="/Portfolio/about/">About</a><a class="text-lg transition-colors duration-300 text-blue-800 hover:text-blue-600" href="/Portfolio/projects/">Projects</a><a class="text-lg transition-colors duration-300 text-blue-800 hover:text-blue-600" href="/Portfolio/skills/">Skills</a><a class="text-lg transition-colors duration-300 text-blue-800 hover:text-blue-600" href="/Portfolio/contact/">Contact</a></nav><button class="md:hidden focus:outline-none text-blue-800 transition-colors duration-300" aria-label="Toggle menu"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600 transition-colors duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg></button></div></header><main class="flex-grow pt-16"><script type="application/ld+json">{"@context":"https://schema.org","@type":"WebSite","name":"About Ankush Gupta","url":"https://ankushgitrepo.github.io/Portfolio/about","description":"About page for Ankush Gupta, an ML Engineer and Full Stack Developer."}</script><section class="py-20 bg-gradient-to-br from-blue-50 to-blue-100 transition-colors duration-500 relative overflow-hidden" id="about"><div class="absolute top-20 left-20 w-64 h-64 rounded-full bg-white opacity-20 blur-3xl animate-blob"></div><div class="absolute bottom-40 right-20 w-72 h-72 rounded-full bg-white opacity-20 blur-3xl animate-blob animation-delay-2000"></div><div class="absolute top-1/2 left-1/3 w-56 h-56 rounded-full bg-white opacity-20 blur-3xl animate-blob animation-delay-4000"></div><div class="absolute bottom-20 left-1/4 w-48 h-48 rounded-full bg-white opacity-20 blur-3xl animate-blob animation-delay-3000"></div><div class="absolute top-40 right-1/4 w-60 h-60 rounded-full bg-white opacity-15 blur-3xl animate-blob animation-delay-1000"></div><div class="absolute bottom-1/3 left-10 w-52 h-52 rounded-full bg-white opacity-15 blur-3xl animate-blob animation-delay-5000"></div><div class="absolute top-10 right-10 w-40 h-40 rounded-full bg-white opacity-10 blur-3xl animate-blob animation-delay-6000"></div><div class="absolute inset-0 bg-noise opacity-[0.03] mix-blend-overlay pointer-events-none"></div><div class="container mx-auto px-4 relative z-10"><div class="text-center mb-16"><h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4 transition-colors duration-300">About Me</h2><div class="w-20 h-1 bg-blue-600 mx-auto transition-colors duration-500"></div></div><div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center"><div class="relative h-96 rounded-lg overflow-hidden shadow-xl bg-blue-100 transition-colors duration-500 backdrop-blur-sm bg-opacity-80"><div class="relative w-full h-full"><div class="absolute inset-0 transition-opacity duration-1000 opacity-100"><div class="relative w-full h-full overflow-hidden"><img alt="Slide 1" decoding="async" data-nimg="fill" class="object-cover object-[center_30%] transition-transform duration-700 hover:scale-105" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="/Portfolio/images/img_1.jpg"/></div></div><div class="absolute inset-0 transition-opacity duration-1000 opacity-0"><div class="relative w-full h-full overflow-hidden"><img alt="Slide 2" loading="lazy" decoding="async" data-nimg="fill" class="object-cover object-[center_45%] transition-transform duration-700 hover:scale-105" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="/Portfolio/images/img_2.jpeg"/></div></div><div class="absolute inset-0 transition-opacity duration-1000 opacity-0"><div class="relative w-full h-full overflow-hidden"><img alt="Slide 3" loading="lazy" decoding="async" data-nimg="fill" class="object-cover object-center transition-transform duration-700 hover:scale-105" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="/Portfolio/images/img_3.jpeg"/></div></div><div class="absolute inset-0 transition-opacity duration-1000 opacity-0"><div class="relative w-full h-full overflow-hidden"><img alt="Slide 4" loading="lazy" decoding="async" data-nimg="fill" class="object-cover object-[center_25%] transition-transform duration-700 hover:scale-105" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="/Portfolio/images/Img_4.jpeg"/></div></div><button class="absolute left-2 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full z-10 transition-colors duration-300" aria-label="Previous slide"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" aria-hidden="true"><path d="m15 18-6-6 6-6"></path></svg></button><button class="absolute right-2 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full z-10 transition-colors duration-300" aria-label="Next slide"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right" aria-hidden="true"><path d="m9 18 6-6-6-6"></path></svg></button><div class="absolute bottom-4 left-0 right-0 flex justify-center gap-2 z-10"><button class="w-2 h-2 rounded-full transition-all duration-300 w-4 bg-blue-600" aria-label="Go to slide 1"></button><button class="w-2 h-2 rounded-full transition-all duration-300 bg-white/70" aria-label="Go to slide 2"></button><button class="w-2 h-2 rounded-full transition-all duration-300 bg-white/70" aria-label="Go to slide 3"></button><button class="w-2 h-2 rounded-full transition-all duration-300 bg-white/70" aria-label="Go to slide 4"></button></div></div></div><div class="relative z-10 p-6 rounded-lg bg-white/30 backdrop-blur-sm shadow-lg"><h3 class="text-2xl font-bold text-blue-600 mb-4 transition-colors duration-500">ML Engineer &amp; Full Stack Developer</h3><p class="text-gray-700 mb-6 transition-colors duration-300">I&#x27;m a passionate ML Engineer and Full Stack Developer with expertise in building intelligent and user-friendly applications. With a strong foundation in both machine learning and web development, I create solutions that are not only technically sound but also deliver exceptional user experiences.</p><p class="text-gray-700 mb-6 transition-colors duration-300">My journey in technology began with a deep curiosity about how things work, which led me to pursue a degree in Computer Science. Since then, I&#x27;ve worked on various projects ranging from predictive analytics systems to responsive web applications.</p><p class="text-gray-700 mb-8 transition-colors duration-300">When I&#x27;m not coding, you can find me exploring new technologies, contributing to open-source projects, or sharing my knowledge through technical writing and mentoring.</p><div class="mt-6"><div class="p-4 rounded-lg bg-blue-100 transition-colors duration-500 max-w-md mx-auto backdrop-blur-sm bg-opacity-80 shadow-lg"><h4 class="text-lg font-semibold text-blue-800 mb-3 transition-colors duration-500">Education</h4><div class="space-y-4"><div class="flex flex-col items-center space-y-3"><a href="https://ljku.edu.in/" target="_blank" rel="noopener noreferrer" class="block transition-transform hover:scale-105 duration-300"><img alt="LJ University Logo" loading="lazy" width="120" height="120" decoding="async" data-nimg="1" class="rounded-lg" style="color:transparent" src="/Portfolio/images/LJ_Logo.png"/></a><div class="text-center"><div class="text-gray-800 font-medium">BE in Computer Science And Information Technology (CSIT)</div><div class="text-sm text-gray-700">LJ University, 2024-Present</div><a href="https://ljku.edu.in/" target="_blank" rel="noopener noreferrer" class="text-sm text-blue-600 hover:underline mt-1 inline-block transition-colors duration-300">Visit University Website</a></div></div></div></div></div></div></div><div class="pb-8"></div></div></section></main><footer class="relative py-12 border-t border-gray-100 overflow-hidden shadow-sm bg-white"><div class="absolute inset-0 z-0"><div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100 opacity-20"></div><div class="absolute inset-0 bg-grid-pattern opacity-3"></div><div class="absolute -right-20 -bottom-20 w-64 h-64 rounded-full bg-blue-600 hover:bg-blue-700 opacity-10"></div><div class="absolute -left-20 -top-20 w-64 h-64 rounded-full bg-blue-600 hover:bg-blue-700 opacity-10"></div><div class="absolute inset-0 bg-noise opacity-[0.02]"></div></div><div class="absolute top-0 left-0 right-0 h-1 bg-blue-600 hover:bg-blue-700 z-10 transition-colors duration-500"></div><div class="absolute top-12 right-12 w-24 h-24 rounded-full bg-blue-600 hover:bg-blue-700 opacity-5 blur-xl"></div><div class="absolute bottom-12 left-12 w-32 h-32 rounded-full bg-blue-600 hover:bg-blue-700 opacity-5 blur-xl"></div><div class="container mx-auto px-4 relative z-20"><div class="relative h-0.5 w-full mb-10 overflow-hidden"><div class="absolute inset-0 bg-blue-600 hover:bg-blue-700 opacity-70"></div><div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent w-1/3 animate-shimmer"></div></div><div class="grid grid-cols-1 md:grid-cols-4 gap-8"><div class="md:col-span-1"><h3 class="text-xl font-bold text-blue-600 mb-4 drop-shadow-sm">Ankush Gupta</h3><p class="text-gray-700 mb-4">ML Engineer &amp; Full Stack Developer specializing in building exceptional digital experiences.</p><div class="flex space-x-4"><a href="https://github.com/AnkushGitRepo" target="_blank" rel="noopener noreferrer" class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" aria-label="GitHub"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"></path></svg></a><a href="https://linkedin.com/in/ankushgupta18" target="_blank" rel="noopener noreferrer" class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" aria-label="LinkedIn"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z"></path></svg></a><a href="https://instagram.com/_ankushg" target="_blank" rel="noopener noreferrer" class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" aria-label="Instagram"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"></path></svg></a><a href="mailto:<EMAIL>" class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" aria-label="Email"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg></a></div></div><div class="md:col-span-1"><h3 class="text-xl font-bold text-blue-600 mb-4 drop-shadow-sm">Main Pages</h3><ul class="space-y-2"><li><a class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" href="/Portfolio/">Home</a></li><li><a class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" href="/Portfolio/about/">About Me</a></li><li><a class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" href="/Portfolio/projects/">Projects</a></li><li><a class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" href="/Portfolio/skills/">Skills</a></li><li><a class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" href="/Portfolio/contact/">Contact Me</a></li></ul></div><div class="md:col-span-1"><h3 class="text-xl font-bold text-blue-600 mb-4 drop-shadow-sm">Resources</h3><ul class="space-y-2"><li><a class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" href="/Portfolio/books/">Books</a></li><li><a class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium" href="/Portfolio/certifications/">Certifications</a></li><li><a href="/resume.pdf" target="_blank" rel="noopener noreferrer" class="text-gray-700 hover:text-blue-600 transition-colors duration-300 font-medium">Resume</a></li></ul></div><div class="md:col-span-1"><h3 class="text-xl font-bold text-blue-600 mb-4 drop-shadow-sm">Contact</h3><p class="text-gray-700 mb-2"><span class="font-medium">Email:</span> <EMAIL></p><p class="text-gray-700 mb-2"><span class="font-medium">Location:</span> Ahmedabad, Gujarat, India</p><p class="text-gray-700 mb-2"><span class="font-medium">Phone:</span> +91 7202906881</p></div></div><div class="border-t border-gray-200 mt-8 pt-8 text-center relative"><div class="absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div><div class="w-20 h-1 bg-blue-600 hover:bg-blue-700 mx-auto mb-6"></div><p class="text-gray-700 font-medium">© <!-- -->2025<!-- --> <span class="text-blue-600">Ankush Gupta</span>. All rights reserved.</p></div></div></footer></div><!--$--><!--/$--><!--$--><!--/$--><script src="/Portfolio/_next/static/chunks/webpack-bb1ce731c3485fda.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[1944,[\"244\",\"static/chunks/244-d2c75447083c6d14.js\",\"766\",\"static/chunks/766-65a6f42e13a3b29f.js\",\"345\",\"static/chunks/app/not-found-9c1ea0fd1bd84912.js\"],\"default\"]\n5:I[1359,[\"244\",\"static/chunks/244-d2c75447083c6d14.js\",\"766\",\"static/chunks/766-65a6f42e13a3b29f.js\",\"725\",\"static/chunks/725-1a48ce81ca026ab7.js\",\"220\",\"static/chunks/app/about/page-884f5e3f6dc04188.js\"],\"ThemeColorProvider\"]\n6:I[3725,[\"244\",\"static/chunks/244-d2c75447083c6d14.js\",\"766\",\"static/chunks/766-65a6f42e13a3b29f.js\",\"725\",\"static/chunks/725-1a48ce81ca026ab7.js\",\"220\",\"static/chunks/app/about/page-884f5e3f6dc04188.js\"],\"default\"]\n7:I[1911,[\"244\",\"static/chunks/244-d2c75447083c6d14.js\",\"766\",\"static/chunks/766-65a6f42e13a3b29f.js\",\"725\",\"static/chunks/725-1a48ce81ca026ab7.js\",\"220\",\"static/chunks/app/about/page-884f5e3f6dc04188.js\"],\"default\"]\n8:I[9665,[],\"MetadataBoundary\"]\na:I[9665,[],\"OutletBoundary\"]\nd:I[4911,[],\"AsyncMetadataOutlet\"]\nf:I[9665,[],\"ViewportBoundary\"]\n11:I[6614,[],\"\"]\n:HL[\"/Portfolio/_next/static/media/569ce4b8f30dc480-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/Portfolio/_next/static/media/93f479601ee12b01-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/Portfolio/_next/static/css/b38ed3232d1ac3ed.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"NdbhGYNvIFHtDN1vFTF84\",\"p\":\"/Portfolio\",\"c\":[\"\",\"about\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"about\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/Portfolio/_next/static/css/b38ed3232d1ac3ed.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"meta\",null,{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"link\",null,{\"rel\":\"icon\",\"href\":\"/Portfolio/favicon.ico\"}]]}],[\"$\",\"body\",null,{\"className\":\"__variable_170e2b __variable_15ed36 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L4\",null,{}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]]}]]}],{\"children\":[\"about\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L5\",null,{\"children\":[\"$\",\"$L6\",null,{\"children\":[[\"$\",\"script\",null,{\"type\":\"application/ld+json\",\"dangerouslySetInnerHTML\":{\"__html\":\"{\\\"@context\\\":\\\"https://schema.org\\\",\\\"@type\\\":\\\"WebSite\\\",\\\"name\\\":\\\"About Ankush Gupta\\\",\\\"url\\\":\\\"https://ankushgitrepo.github.io/Portfolio/about\\\",\\\"description\\\":\\\"About page for Ankush Gupta, an ML Engineer and Full Stack Developer.\\\"}\"}}],[\"$\",\"$L7\",null,{}]]}]}],[\"$\",\"$L8\",null,{\"children\":\"$L9\"}],null,[\"$\",\"$La\",null,{\"children\":[\"$Lb\",\"$Lc\",[\"$\",\"$Ld\",null,{\"promise\":\"$@e\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"_WlqXYMrOYCXmmsNm5v68\",{\"children\":[[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"12:\"$Sreact.suspense\"\n13:I[4911,[],\"AsyncMetadata\"]\n9:[\"$\",\"$12\",null,{\"fallback\":null,\"children\":[\"$\",\"$L13\",null,{\"promise\":\"$@14\"}]}]\n"])</script><script>self.__next_f.push([1,"c:null\n"])</script><script>self.__next_f.push([1,"10:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nb:null\n"])</script><script>self.__next_f.push([1,"14:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"About | Ankush Gupta | Ankush Gupta\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Learn more about Ankush Gupta, an ML Engineer and Full Stack Developer with expertise in building intelligent and user-friendly applications.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Ankush Gupta Portfolio\"}],[\"$\",\"meta\",\"3\",{\"name\":\"author\",\"content\":\"Ankush Gupta\"}],[\"$\",\"meta\",\"4\",{\"name\":\"generator\",\"content\":\"Next.js\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"About Ankush Gupta, ML Engineer, Full Stack Developer, Experience, Education\"}],[\"$\",\"meta\",\"6\",{\"name\":\"referrer\",\"content\":\"origin-when-cross-origin\"}],[\"$\",\"meta\",\"7\",{\"name\":\"creator\",\"content\":\"Ankush Gupta\"}],[\"$\",\"meta\",\"8\",{\"name\":\"publisher\",\"content\":\"Ankush Gupta\"}],[\"$\",\"meta\",\"9\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"10\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"link\",\"11\",{\"rel\":\"canonical\",\"href\":\"https://ankushgitrepo.github.io/Portfolio/\"}],[\"$\",\"meta\",\"12\",{\"name\":\"format-detection\",\"content\":\"telephone=no, address=no, email=no\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:title\",\"content\":\"About | Ankush Gupta\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:description\",\"content\":\"Learn more about Ankush Gupta, an ML Engineer and Full Stack Developer with expertise in building intelligent and user-friendly applications.\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:image\",\"content\":\"https://ankushgitrepo.github.io/Portfolio/Portfolio/images/og-image.jpg\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"18\",{\"property\":\"og:image:alt\",\"content\":\"About | Ankush Gupta\"}],[\"$\",\"meta\",\"19\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:title\",\"content\":\"About | Ankush Gupta\"}],[\"$\",\"meta\",\"22\",{\"name\":\"twitter:description\",\"content\":\"Learn more about Ankush Gupta, an ML Engineer and Full Stack Developer with expertise in building intelligent and user-friendly applications.\"}],[\"$\",\"meta\",\"23\",{\"name\":\"twitter:image\",\"content\":\"https://ankushgitrepo.github.io/Portfolio/Portfolio/images/og-image.jpg\"}],[\"$\",\"link\",\"24\",{\"rel\":\"icon\",\"href\":\"/Portfolio/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"e:{\"metadata\":\"$14:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>