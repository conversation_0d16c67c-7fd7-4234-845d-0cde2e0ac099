1:"$Sreact.fragment"
2:I[7555,[],""]
3:I[1295,[],""]
4:I[1944,["244","static/chunks/244-d2c75447083c6d14.js","766","static/chunks/766-65a6f42e13a3b29f.js","345","static/chunks/app/not-found-9c1ea0fd1bd84912.js"],"default"]
5:I[1359,["244","static/chunks/244-d2c75447083c6d14.js","766","static/chunks/766-65a6f42e13a3b29f.js","725","static/chunks/725-1a48ce81ca026ab7.js","220","static/chunks/app/about/page-884f5e3f6dc04188.js"],"ThemeColorProvider"]
6:I[3725,["244","static/chunks/244-d2c75447083c6d14.js","766","static/chunks/766-65a6f42e13a3b29f.js","725","static/chunks/725-1a48ce81ca026ab7.js","220","static/chunks/app/about/page-884f5e3f6dc04188.js"],"default"]
7:I[1911,["244","static/chunks/244-d2c75447083c6d14.js","766","static/chunks/766-65a6f42e13a3b29f.js","725","static/chunks/725-1a48ce81ca026ab7.js","220","static/chunks/app/about/page-884f5e3f6dc04188.js"],"default"]
8:I[9665,[],"MetadataBoundary"]
a:I[9665,[],"OutletBoundary"]
d:I[4911,[],"AsyncMetadataOutlet"]
f:I[9665,[],"ViewportBoundary"]
11:I[6614,[],""]
:HL["/Portfolio/_next/static/media/569ce4b8f30dc480-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/Portfolio/_next/static/media/93f479601ee12b01-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
:HL["/Portfolio/_next/static/css/b38ed3232d1ac3ed.css","style"]
0:{"P":null,"b":"NdbhGYNvIFHtDN1vFTF84","p":"/Portfolio","c":["","about",""],"i":false,"f":[[["",{"children":["about",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/Portfolio/_next/static/css/b38ed3232d1ac3ed.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[["$","meta",null,{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","link",null,{"rel":"icon","href":"/Portfolio/favicon.ico"}]]}],["$","body",null,{"className":"__variable_170e2b __variable_15ed36 antialiased","children":["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[["$","$L4",null,{}],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]]}]]}],{"children":["about",["$","$1","c",{"children":[null,["$","$L2",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L3",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","$L5",null,{"children":["$","$L6",null,{"children":[["$","script",null,{"type":"application/ld+json","dangerouslySetInnerHTML":{"__html":"{\"@context\":\"https://schema.org\",\"@type\":\"WebSite\",\"name\":\"About Ankush Gupta\",\"url\":\"https://ankushgitrepo.github.io/Portfolio/about\",\"description\":\"About page for Ankush Gupta, an ML Engineer and Full Stack Developer.\"}"}}],["$","$L7",null,{}]]}]}],["$","$L8",null,{"children":"$L9"}],null,["$","$La",null,{"children":["$Lb","$Lc",["$","$Ld",null,{"promise":"$@e"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","_WlqXYMrOYCXmmsNm5v68",{"children":[["$","$Lf",null,{"children":"$L10"}],["$","meta",null,{"name":"next-size-adjust","content":""}]]}],null]}],false]],"m":"$undefined","G":["$11","$undefined"],"s":false,"S":true}
12:"$Sreact.suspense"
13:I[4911,[],"AsyncMetadata"]
9:["$","$12",null,{"fallback":null,"children":["$","$L13",null,{"promise":"$@14"}]}]
c:null
10:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
b:null
14:{"metadata":[["$","title","0",{"children":"About | Ankush Gupta | Ankush Gupta"}],["$","meta","1",{"name":"description","content":"Learn more about Ankush Gupta, an ML Engineer and Full Stack Developer with expertise in building intelligent and user-friendly applications."}],["$","meta","2",{"name":"application-name","content":"Ankush Gupta Portfolio"}],["$","meta","3",{"name":"author","content":"Ankush Gupta"}],["$","meta","4",{"name":"generator","content":"Next.js"}],["$","meta","5",{"name":"keywords","content":"About Ankush Gupta, ML Engineer, Full Stack Developer, Experience, Education"}],["$","meta","6",{"name":"referrer","content":"origin-when-cross-origin"}],["$","meta","7",{"name":"creator","content":"Ankush Gupta"}],["$","meta","8",{"name":"publisher","content":"Ankush Gupta"}],["$","meta","9",{"name":"robots","content":"index, follow"}],["$","meta","10",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","link","11",{"rel":"canonical","href":"https://ankushgitrepo.github.io/Portfolio/"}],["$","meta","12",{"name":"format-detection","content":"telephone=no, address=no, email=no"}],["$","meta","13",{"property":"og:title","content":"About | Ankush Gupta"}],["$","meta","14",{"property":"og:description","content":"Learn more about Ankush Gupta, an ML Engineer and Full Stack Developer with expertise in building intelligent and user-friendly applications."}],["$","meta","15",{"property":"og:image","content":"https://ankushgitrepo.github.io/Portfolio/Portfolio/images/og-image.jpg"}],["$","meta","16",{"property":"og:image:width","content":"1200"}],["$","meta","17",{"property":"og:image:height","content":"630"}],["$","meta","18",{"property":"og:image:alt","content":"About | Ankush Gupta"}],["$","meta","19",{"property":"og:type","content":"website"}],["$","meta","20",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","21",{"name":"twitter:title","content":"About | Ankush Gupta"}],["$","meta","22",{"name":"twitter:description","content":"Learn more about Ankush Gupta, an ML Engineer and Full Stack Developer with expertise in building intelligent and user-friendly applications."}],["$","meta","23",{"name":"twitter:image","content":"https://ankushgitrepo.github.io/Portfolio/Portfolio/images/og-image.jpg"}],["$","link","24",{"rel":"icon","href":"/Portfolio/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
e:{"metadata":"$14:metadata","error":null,"digest":"$undefined"}
